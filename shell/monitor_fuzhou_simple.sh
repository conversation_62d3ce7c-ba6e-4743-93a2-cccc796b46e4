#!/bin/bash

# 福州政府网站监控脚本 - 简化版
# 作者: linshiqiang
# 日期: 2025-01-16
# 描述: 监控福州政府网站搜索接口的可用性和响应

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 配置参数
URL="https://www.fuzhou.gov.cn/fjdzapp/search"
LOG_FILE="${SCRIPT_DIR}/monitor_fuzhou_simple.log"
SENT_URLS_FILE="${SCRIPT_DIR}/sent_urls.txt"  # 已发送URL记录文件
MAX_RESPONSE_TIME=10  # 最大响应时间（秒）
WECHAT_WEBHOOK="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7c913aae-3fc2-495a-b2ff-755437d07b43"  # 企业微信机器人webhook地址

# 搜索查询参数配置
CHANNEL_ID="229105"                           # 频道ID
SORT_FIELD="-docorderpri,-docreltime"        # 排序字段
CHANNEL_SQL="(chnlid=1022)*(doctitle=%保障性%)"  # 查询条件：频道ID=1022且标题包含"保障性"
CLASS_COL="publishyear"                      # 分类列
CLASS_NUM="100"                              # 分类数量
CLASS_SORT="0"                               # 分类排序
CACHE="true"                                 # 是否使用缓存
PAGE="1"                                     # 页码
PRE_PAGE="75"                                # 每页数量

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    shift
    local message="$@"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

# URL编码函数
url_encode() {
    local string="$1"
    python3 -c "
import urllib.parse
import sys
print(urllib.parse.quote('$string', safe=''))
"
}

# 构建查询参数
build_query_params() {
    # 对需要编码的参数进行URL编码
    local encoded_sort_field=$(url_encode "$SORT_FIELD")
    local encoded_channel_sql=$(url_encode "$CHANNEL_SQL")
    local encoded_class_col=$(url_encode "$CLASS_COL")

    # 构建完整的查询参数字符串
    echo "channelid=${CHANNEL_ID}&sortfield=${encoded_sort_field}&classsql=${encoded_channel_sql}&classcol=${encoded_class_col}&classnum=${CLASS_NUM}&classsort=${CLASS_SORT}&cache=${CACHE}&page=${PAGE}&prepage=${PRE_PAGE}"
}

# 获取今日日期（格式：2025-07-16）
get_today_date() {
    date '+%Y-%m-%d'
}

# 检查URL是否已发送过
is_url_sent() {
    local url="$1"

    # 如果记录文件不存在，返回未发送
    if [ ! -f "$SENT_URLS_FILE" ]; then
        return 1
    fi

    # 检查URL是否在记录文件中
    if grep -Fxq "$url" "$SENT_URLS_FILE"; then
        return 0  # 已发送
    else
        return 1  # 未发送
    fi
}

# 记录已发送的URL
record_sent_url() {
    local url="$1"

    # 确保记录文件存在
    touch "$SENT_URLS_FILE"

    # 追加URL到记录文件
    echo "$url" >> "$SENT_URLS_FILE"

    log "INFO" "${BLUE}已记录URL到发送历史: $url${NC}"
}

# 清理过期的URL记录（可选，保留最近30天的记录）
cleanup_old_records() {
    if [ ! -f "$SENT_URLS_FILE" ]; then
        return 0
    fi

    # 创建临时文件
    local temp_file=$(mktemp)

    # 获取30天前的日期
    local cutoff_date
    if command -v gdate &> /dev/null; then
        # macOS with GNU date
        cutoff_date=$(gdate -d '30 days ago' '+%Y-%m-%d')
    elif date -v-30d &> /dev/null 2>&1; then
        # macOS with BSD date
        cutoff_date=$(date -v-30d '+%Y-%m-%d')
    else
        # Linux with GNU date
        cutoff_date=$(date -d '30 days ago' '+%Y-%m-%d')
    fi

    # 保留包含最近30天日期的URL记录
    if [ -n "$cutoff_date" ]; then
        grep -E "202[0-9]-[0-9]{2}-[0-9]{2}" "$SENT_URLS_FILE" | \
        while IFS= read -r url; do
            # 提取URL中的日期
            url_date=$(echo "$url" | grep -oE '202[0-9][0-9][0-9][0-9][0-9]' | sed 's/\(202[0-9]\)\([0-9][0-9]\)\([0-9][0-9]\)/\1-\2-\3/')
            if [ -n "$url_date" ] && [ "$url_date" \> "$cutoff_date" ]; then
                echo "$url" >> "$temp_file"
            fi
        done

        # 替换原文件
        if [ -s "$temp_file" ]; then
            mv "$temp_file" "$SENT_URLS_FILE"
            log "INFO" "${BLUE}已清理过期的URL记录${NC}"
        else
            rm -f "$temp_file"
        fi
    else
        rm -f "$temp_file"
    fi
}

# 发送企业微信消息
send_wechat_message() {
    local message="$1"

    if [ -z "$WECHAT_WEBHOOK" ]; then
        log "WARN" "${YELLOW}企业微信webhook未配置，跳过消息发送${NC}"
        return 0
    fi

    # 构建消息体
    local json_data=$(cat <<EOF
{
    "msgtype": "markdown",
    "markdown": {
        "content": "$message"
    }
}
EOF
)

    # 发送消息
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$json_data" \
        "$WECHAT_WEBHOOK")

    local curl_exit_code=$?

    if [ $curl_exit_code -eq 0 ]; then
        log "SUCCESS" "${GREEN}企业微信消息发送成功${NC}"
        log "INFO" "${BLUE}响应: $response${NC}"
    else
        log "ERROR" "${RED}企业微信消息发送失败，curl退出码: $curl_exit_code${NC}"
    fi
}

# 解析响应数据并处理今日数据
process_response_data() {
    local response_body="$1"
    local today_date=$(get_today_date)

    log "INFO" "${BLUE}开始解析响应数据，查找今日($today_date)的数据...${NC}"

    # 检查响应是否包含data字段
    if ! echo "$response_body" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if 'data' not in data or not isinstance(data['data'], list):
        sys.exit(1)
    print('数据格式正确')
except:
    sys.exit(1)
" 2>/dev/null; then
        log "ERROR" "${RED}响应数据格式错误或不包含data字段${NC}"
        return 1
    fi

    # 使用Python解析JSON并筛选今日数据
    local today_items=$(echo "$response_body" | python3 -c "
import json, sys
from datetime import datetime

try:
    data = json.load(sys.stdin)
    today_date = '$today_date'
    today_items = []

    for item in data.get('data', []):
        time_str = item.get('time', '')
        if time_str.startswith(today_date):
            today_items.append(item)

    print(json.dumps(today_items, ensure_ascii=False, indent=2))
except Exception as e:
    print(f'解析错误: {e}', file=sys.stderr)
    sys.exit(1)
")

    if [ $? -ne 0 ]; then
        log "ERROR" "${RED}Python解析数据失败${NC}"
        return 1
    fi

    # 检查是否有今日数据
    local item_count=$(echo "$today_items" | python3 -c "
import json, sys
try:
    items = json.load(sys.stdin)
    print(len(items))
except:
    print(0)
")

    if [ "$item_count" -eq 0 ]; then
        log "INFO" "${BLUE}今日($today_date)暂无新数据${NC}"
        return 0
    fi

    log "SUCCESS" "${GREEN}发现 $item_count 条今日数据，开始处理...${NC}"

    # 处理每条今日数据
    echo "$today_items" | python3 -c "
import json, sys

def send_item_to_wechat(item):
    import datetime

    chnldocurl = item.get('chnldocurl', '')
    docabstract = item.get('docabstract', '')
    doctitle = item.get('doctitle', '').replace('<font color=red>', '').replace('</font>', '')
    time_str = item.get('time', '')
    files = item.get('files', [])

    # 获取当前发送时间
    send_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 构建markdown消息
    message_parts = []
    message_parts.append(f'## 📢 福州政府新公告')
    message_parts.append(f'**标题**: {doctitle}')
    message_parts.append(f'**发布时间**: {time_str}')
    message_parts.append(f'**推送时间**: {send_time}')
    message_parts.append(f'**摘要**: {docabstract[:200]}...' if len(docabstract) > 200 else f'**摘要**: {docabstract}')

    if chnldocurl:
        message_parts.append(f'**详情链接**: [点击查看]({chnldocurl})')

    # 处理附件文件
    if files:
        message_parts.append(f'**附件文件**:')
        for file_item in files:
            file_desc = file_item.get('appdesc', '附件')
            file_href = file_item.get('_href', '')
            if file_href:
                message_parts.append(f'- [{file_desc}]({file_href})')
            else:
                message_parts.append(f'- {file_desc}')

    message_parts.append(f'---')
    message_parts.append(f'*福州政府网站监控 - 推送于 {send_time}*')

    return '\\n'.join(message_parts)

try:
    items = json.load(sys.stdin)
    for item in items:
        chnldocurl = item.get('chnldocurl', '')
        message = send_item_to_wechat(item)
        print('ITEM_START')
        print(f'URL:{chnldocurl}')
        print('WECHAT_MESSAGE_START')
        print(message)
        print('WECHAT_MESSAGE_END')
        print('ITEM_END')
        print('---ITEM_SEPARATOR---')
except Exception as e:
    print(f'处理数据错误: {e}', file=sys.stderr)
    sys.exit(1)
" | while IFS= read -r line; do
        if [ "$line" = "ITEM_START" ]; then
            current_url=""
            message=""
            reading_message=false
        elif [[ "$line" =~ ^URL: ]]; then
            current_url="${line#URL:}"
        elif [ "$line" = "WECHAT_MESSAGE_START" ]; then
            message=""
            reading_message=true
        elif [ "$line" = "WECHAT_MESSAGE_END" ]; then
            reading_message=false
        elif [ "$line" = "ITEM_END" ]; then
            # 检查URL是否已发送过
            if [ -n "$current_url" ]; then
                if is_url_sent "$current_url"; then
                    log "INFO" "${YELLOW}URL已发送过，跳过: $current_url${NC}"
                else
                    if [ -n "$message" ]; then
                        send_wechat_message "$message"
                        record_sent_url "$current_url"
                        sleep 1  # 避免发送过快
                    fi
                fi
            fi
        elif [ "$line" = "---ITEM_SEPARATOR---" ]; then
            continue
        elif [ "$reading_message" = true ]; then
            if [ -z "$message" ]; then
                message="$line"
            else
                message="$message\n$line"
            fi
        fi
    done

    log "SUCCESS" "${GREEN}今日数据处理完成${NC}"
}

# 调用福州政府搜索接口
call_fuzhou_api() {
    log "INFO" "${BLUE}开始调用福州政府搜索接口...${NC}"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 创建临时文件存储响应
    local temp_response=$(mktemp)

    # 构建查询参数
    local query_params=$(build_query_params)
    log "INFO" "${BLUE}查询参数: $query_params${NC}"

    # 记录开始时间
    local start_time=$(date +%s)

    # 调用接口
    local http_code=$(curl -s \
        -X POST \
        -w "%{http_code}" \
        -o "$temp_response" \
        --max-time ${MAX_RESPONSE_TIME} \
        'https://www.fuzhou.gov.cn/fjdzapp/search' \
        -H 'Accept: application/json, text/javascript, */*; q=0.01' \
        -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
        -H 'Connection: keep-alive' \
        -H 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8' \
        -b 'Secure; Hm_lvt_f1010fcb400a1b17bc43a071742486bb=**********; HMACCOUNT=A7E4D585CCB766C8; BFreeDialect=0; _trs_uv=mcj0jbnt_6103_307z; Secure; BFreeSecret=%7B%22code%22%3A200%2C%22sn%22%3A%220788226104547083%22%2C%22asr%22%3A%5B%7B%22desc%22%3A%22%E6%99%AE%E9%80%9A%E8%AF%9D%22%2C%22name%22%3A%22pth%22%2C%22proName%22%3A%22baidu%22%2C%22soundType%22%3A%221%22%7D%5D%7D; CPS_SESSION=8C1D19B3C013EE72494641EBEFA84591; _gscu_1374281241=52661586mliw4214; _gscbrs_1374281241=1; Hm_lvt_61=**********; Hm_lpvt_61=**********; Hm_lvt_4=**********; Hm_lpvt_4=**********; Hm_lpvt_f1010fcb400a1b17bc43a071742486bb=**********' \
        -H 'Origin: https://www.fuzhou.gov.cn' \
        -H 'Referer: https://www.fuzhou.gov.cn/zwgk/tzgg/' \
        -H 'Sec-Fetch-Dest: empty' \
        -H 'Sec-Fetch-Mode: cors' \
        -H 'Sec-Fetch-Site: same-origin' \
        -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36' \
        -H 'X-Requested-With: XMLHttpRequest' \
        -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
        -H 'sec-ch-ua-mobile: ?0' \
        -H 'sec-ch-ua-platform: "macOS"' \
        --data-raw "$query_params")
    
    # 获取curl的退出状态
    local curl_exit_code=$?
    
    # 计算耗时
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 读取响应内容
    local response_body=$(cat "$temp_response" 2>/dev/null)
    local response_size=$(wc -c < "$temp_response" 2>/dev/null || echo "0")
    
    # 清理临时文件
    rm -f "$temp_response"
    
    # 输出基本信息
    log "INFO" "${BLUE}HTTP状态码: ${http_code}${NC}"
    log "INFO" "${BLUE}响应大小: ${response_size} 字节${NC}"
    log "INFO" "${BLUE}总耗时: ${duration} 秒${NC}"
    log "INFO" "${BLUE}curl退出码: ${curl_exit_code}${NC}"
    
    # 输出响应内容（可选，用于调试）
    if [ "${SHOW_RESPONSE:-false}" = "true" ]; then
        echo ""
        echo "==================== 响应内容 ===================="
        if [ -n "$response_body" ] && [ "$response_size" -gt 0 ]; then
            # 尝试格式化JSON，如果失败则直接输出
            if command -v python3 &> /dev/null; then
                echo "$response_body" | python3 -m json.tool 2>/dev/null || echo "$response_body"
            elif command -v jq &> /dev/null; then
                echo "$response_body" | jq . 2>/dev/null || echo "$response_body"
            else
                echo "$response_body"
            fi
        else
            log "WARN" "${YELLOW}响应内容为空或无效${NC}"
        fi
        echo "=================================================="
        echo ""
    fi

    # 检查响应状态
    if [ "$curl_exit_code" -eq 0 ] && [ "$http_code" = "200" ]; then
        log "SUCCESS" "${GREEN}接口调用成功 (HTTP 200)${NC}"

        # 处理响应数据
        if [ -n "$response_body" ] && [ "$response_size" -gt 0 ]; then
            process_response_data "$response_body"
        else
            log "WARN" "${YELLOW}响应内容为空，跳过数据处理${NC}"
        fi

        return 0
    else
        log "ERROR" "${RED}接口调用失败 (HTTP ${http_code}, curl退出码: ${curl_exit_code})${NC}"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "福州政府网站监控脚本 - 简化版"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help       显示此帮助信息"
    echo "  -l, --log        指定日志文件路径 (默认: monitor_fuzhou_simple.log)"
    echo "  -t, --timeout    设置最大响应时间 (默认: 10秒)"
    echo "  -w, --webhook    设置企业微信机器人webhook地址"
    echo "  -s, --show       显示完整响应内容 (调试用)"
    echo ""
    echo "环境变量:"
    echo "  WECHAT_WEBHOOK   企业微信机器人webhook地址"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置运行"
    echo "  $0 -l /tmp/monitor.log                # 指定日志文件"
    echo "  $0 -t 15                              # 设置15秒超时"
    echo "  $0 -w 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx'  # 设置企业微信webhook"
    echo "  $0 -s                                 # 显示完整响应内容"
    echo ""
    echo "企业微信机器人配置:"
    echo "  1. 在企业微信群中添加机器人"
    echo "  2. 获取webhook地址"
    echo "  3. 使用 -w 参数或设置 WECHAT_WEBHOOK 环境变量"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -l|--log)
            LOG_FILE="$2"
            shift 2
            ;;
        -t|--timeout)
            MAX_RESPONSE_TIME="$2"
            shift 2
            ;;
        -w|--webhook)
            WECHAT_WEBHOOK="$2"
            shift 2
            ;;
        -s|--show)
            SHOW_RESPONSE="true"
            shift
            ;;
        *)
            log "ERROR" "${RED}未知参数: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 如果没有通过参数设置webhook，尝试从环境变量获取
if [ -z "$WECHAT_WEBHOOK" ] && [ -n "${WECHAT_WEBHOOK_ENV:-}" ]; then
    WECHAT_WEBHOOK="$WECHAT_WEBHOOK_ENV"
fi

# 检查依赖
if ! command -v curl &> /dev/null; then
    log "ERROR" "${RED}curl 命令未找到，请先安装 curl${NC}"
    exit 1
fi

if ! command -v python3 &> /dev/null; then
    log "ERROR" "${RED}python3 命令未找到，请先安装 python3${NC}"
    exit 1
fi

# 创建日志文件
touch "${LOG_FILE}" || {
    log "ERROR" "${RED}无法创建日志文件: ${LOG_FILE}${NC}"
    exit 1
}

# 开始监控
log "INFO" "${BLUE}开始监控福州政府网站搜索接口${NC}"
log "INFO" "${BLUE}目标URL: ${URL}${NC}"
log "INFO" "${BLUE}最大响应时间: ${MAX_RESPONSE_TIME}秒${NC}"
log "INFO" "${BLUE}日志文件: ${LOG_FILE}${NC}"
log "INFO" "${BLUE}URL记录文件: ${SENT_URLS_FILE}${NC}"
if [ -n "$WECHAT_WEBHOOK" ]; then
    log "INFO" "${BLUE}企业微信推送: 已配置${NC}"
else
    log "WARN" "${YELLOW}企业微信推送: 未配置${NC}"
fi

# 清理过期记录（可选）
cleanup_old_records

echo ""

# 调用接口
if call_fuzhou_api; then
    log "SUCCESS" "${GREEN}监控完成 - 接口正常${NC}"
    exit 0
else
    log "ERROR" "${RED}监控完成 - 接口异常${NC}"
    exit 1
fi
