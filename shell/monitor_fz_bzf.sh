#!/bin/bash

# 福州政府网站监控脚本
# 作者: linshiqiang
# 日期: 2025-01-16
# 描述: 监控福州政府网站搜索接口的可用性和响应

# 配置参数
URL="https://www.fuzhou.gov.cn/fjdzapp/search"
LOG_FILE="monitor_fuzhou_gov.log"
MAX_RESPONSE_TIME=10  # 最大响应时间（秒）
RETRY_COUNT=3         # 重试次数

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    shift
    local message="$@"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

# 信息日志
log_info() {
    log "INFO" "${BLUE}$@${NC}"
}

# 成功日志
log_success() {
    log "SUCCESS" "${GREEN}$@${NC}"
}

# 警告日志
log_warn() {
    log "WARN" "${YELLOW}$@${NC}"
}

# 错误日志
log_error() {
    log "ERROR" "${RED}$@${NC}"
}

# 调用福州政府搜索接口
call_fuzhou_api() {
    local attempt=$1
    
    log_info "第 ${attempt} 次尝试调用福州政府搜索接口..."
    
    # 记录开始时间
    local start_time=$(date +%s.%N)
    
    # 调用接口
    local response=$(curl -s -w "\n%{http_code}\n%{time_total}" \
        'https://www.fuzhou.gov.cn/fjdzapp/search' \
        -H 'Accept: application/json, text/javascript, */*; q=0.01' \
        -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
        -H 'Connection: keep-alive' \
        -H 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8' \
        -b 'Secure; Hm_lvt_f1010fcb400a1b17bc43a071742486bb=**********; HMACCOUNT=A7E4D585CCB766C8; BFreeDialect=0; _trs_uv=mcj0jbnt_6103_307z; Secure; BFreeSecret=%7B%22code%22%3A200%2C%22sn%22%3A%220788226104547083%22%2C%22asr%22%3A%5B%7B%22desc%22%3A%22%E6%99%AE%E9%80%9A%E8%AF%9D%22%2C%22name%22%3A%22pth%22%2C%22proName%22%3A%22baidu%22%2C%22soundType%22%3A%221%22%7D%5D%7D; CPS_SESSION=8C1D19B3C013EE72494641EBEFA84591; _gscu_1374281241=52661586mliw4214; _gscbrs_1374281241=1; Hm_lvt_61=**********; Hm_lpvt_61=**********; Hm_lvt_4=**********; Hm_lpvt_4=**********; Hm_lpvt_f1010fcb400a1b17bc43a071742486bb=**********' \
        -H 'Origin: https://www.fuzhou.gov.cn' \
        -H 'Referer: https://www.fuzhou.gov.cn/zwgk/tzgg/' \
        -H 'Sec-Fetch-Dest: empty' \
        -H 'Sec-Fetch-Mode: cors' \
        -H 'Sec-Fetch-Site: same-origin' \
        -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36' \
        -H 'X-Requested-With: XMLHttpRequest' \
        -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
        -H 'sec-ch-ua-mobile: ?0' \
        -H 'sec-ch-ua-platform: "macOS"' \
        --data-raw 'channelid=229105&sortfield=-docorderpri%2C-docreltime&classsql=(chnlid%3D1022)*(doctitle%3D%25%E4%BF%9D%E9%9A%9C%E6%80%A7%25)&classcol=publishyear&classnum=100&classsort=0&cache=true&page=1&prepage=75' \
        --max-time ${MAX_RESPONSE_TIME})
    
    # 解析响应
    local response_body=$(echo "$response" | head -n -2)
    local http_code=$(echo "$response" | tail -n 2 | head -n 1)
    local response_time=$(echo "$response" | tail -n 1)
    
    # 记录结束时间
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    
    log_info "HTTP状态码: ${http_code}"
    log_info "响应时间: ${response_time}秒"
    log_info "总耗时: ${duration}秒"
    
    # 输出响应内容
    echo "==================== 响应内容 ===================="
    if [ -n "$response_body" ]; then
        echo "$response_body" | python3 -m json.tool 2>/dev/null || echo "$response_body"
    else
        log_warn "响应内容为空"
    fi
    echo "=================================================="
    
    # 检查响应状态
    if [ "$http_code" = "200" ]; then
        log_success "接口调用成功 (HTTP 200)"
        return 0
    else
        log_error "接口调用失败 (HTTP ${http_code})"
        return 1
    fi
}

# 主监控函数
monitor() {
    log_info "开始监控福州政府网站搜索接口"
    log_info "目标URL: ${URL}"
    log_info "最大响应时间: ${MAX_RESPONSE_TIME}秒"
    log_info "重试次数: ${RETRY_COUNT}"
    echo ""
    
    local success=false
    
    for i in $(seq 1 $RETRY_COUNT); do
        if call_fuzhou_api $i; then
            success=true
            break
        else
            if [ $i -lt $RETRY_COUNT ]; then
                log_warn "等待3秒后重试..."
                sleep 3
            fi
        fi
        echo ""
    done
    
    if [ "$success" = true ]; then
        log_success "监控完成 - 接口正常"
        exit 0
    else
        log_error "监控完成 - 接口异常，所有重试均失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "福州政府网站监控脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -l, --log      指定日志文件路径 (默认: monitor_fuzhou_gov.log)"
    echo "  -t, --timeout  设置最大响应时间 (默认: 10秒)"
    echo "  -r, --retry    设置重试次数 (默认: 3次)"
    echo ""
    echo "示例:"
    echo "  $0                           # 使用默认配置运行"
    echo "  $0 -l /tmp/monitor.log       # 指定日志文件"
    echo "  $0 -t 15 -r 5               # 设置15秒超时，重试5次"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -l|--log)
            LOG_FILE="$2"
            shift 2
            ;;
        -t|--timeout)
            MAX_RESPONSE_TIME="$2"
            shift 2
            ;;
        -r|--retry)
            RETRY_COUNT="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖
if ! command -v curl &> /dev/null; then
    log_error "curl 命令未找到，请先安装 curl"
    exit 1
fi

if ! command -v bc &> /dev/null; then
    log_error "bc 命令未找到，请先安装 bc"
    exit 1
fi

# 创建日志文件
touch "${LOG_FILE}" || {
    log_error "无法创建日志文件: ${LOG_FILE}"
    exit 1
}

# 开始监控
monitor
